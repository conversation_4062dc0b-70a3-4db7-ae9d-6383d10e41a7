import React, { useState, useEffect, useRef } from "react";
import { Calendar, Settings, Archive, Download } from "lucide-react";
import { Timeline } from "./components/Timeline";
import { FloatingActionButtons } from "./components/FloatingActionButtons";
import { AddEntryModal } from "./components/AddEntryModal";
import { AddTimeBlockModal } from "./components/AddTimeBlockModal";
import { BacklogModal } from "./components/BacklogModal";
import { Entry, TimeBlock } from "./types";
import { exportTimelineToPDF } from "./utils";

function App() {
	const [entries, setEntries] = useState<Entry[]>([]);
	const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>([]);
	const [isAddEntryModalOpen, setIsAddEntryModalOpen] = useState(false);
	const [isAddTimeBlockModalOpen, setIsAddTimeBlockModalOpen] = useState(false);
	const [isBacklogModalOpen, setIsBacklogModalOpen] = useState(false);
	const [selectedEntryType, setSelectedEntryType] = useState<
		"note" | "task" | "event" | undefined
	>();
	const timelineRef = useRef<HTMLDivElement>(null);

	// PDF Export function
	const handleExportPDF = async () => {
		if (timelineRef.current) {
			await exportTimelineToPDF(timelineRef.current, {
				filename: `timeline-export-${
					new Date().toISOString().split("T")[0]
				}.pdf`,
			});
		}
	};

	// Load data from localStorage on mount
	useEffect(() => {
		const savedEntries = localStorage.getItem("timeline-entries");
		const savedTimeBlocks = localStorage.getItem("timeline-timeblocks");

		if (savedEntries) {
			const parsedEntries = JSON.parse(savedEntries).map((entry: any) => ({
				...entry,
				timestamp: new Date(entry.timestamp),
			}));
			setEntries(parsedEntries);
		}

		if (savedTimeBlocks) {
			const parsedTimeBlocks = JSON.parse(savedTimeBlocks).map(
				(block: any) => ({
					...block,
					startTime: new Date(block.startTime),
					endTime: new Date(block.endTime),
				})
			);
			setTimeBlocks(parsedTimeBlocks);
		}
	}, []);

	// Save to localStorage whenever entries or timeBlocks change
	useEffect(() => {
		localStorage.setItem("timeline-entries", JSON.stringify(entries));
	}, [entries]);

	useEffect(() => {
		localStorage.setItem("timeline-timeblocks", JSON.stringify(timeBlocks));
	}, [timeBlocks]);

	const handleAddEntry = (entry: Entry) => {
		setEntries((prev) => [...prev, entry]);
	};

	const handleAddTimeBlock = (timeBlock: TimeBlock) => {
		setTimeBlocks((prev) => [...prev, timeBlock]);
	};

	const handleToggleComplete = (id: string) => {
		setEntries((prev) =>
			prev.map((entry) =>
				entry.id === id ? { ...entry, completed: !entry.completed } : entry
			)
		);
	};

	const handleDeleteEntry = (id: string) => {
		setEntries((prev) => prev.filter((entry) => entry.id !== id));
	};

	const handleDeleteTimeBlock = (id: string) => {
		setTimeBlocks((prev) => prev.filter((block) => block.id !== id));
	};

	const handleFloatingButtonAddEntry = (type: "note" | "task" | "event") => {
		setSelectedEntryType(type);
		setIsAddEntryModalOpen(true);
	};

	const handleFloatingButtonAddTimeBlock = () => {
		setIsAddTimeBlockModalOpen(true);
	};

	const handleCloseAddEntryModal = () => {
		setIsAddEntryModalOpen(false);
		setSelectedEntryType(undefined);
	};

	const handleScheduleEntry = (entryId: string, date: string, time: string) => {
		setEntries((prev) =>
			prev.map((entry) => {
				if (entry.id === entryId) {
					const today = new Date();
					today.setHours(0, 0, 0, 0);

					let newDate: Date;
					if (date === "tomorrow") {
						newDate = new Date(today.getTime() + 24 * 60 * 60 * 1000);
					} else {
						newDate = today;
					}

					const [hours, minutes] = time.split(":").map(Number);
					newDate.setHours(hours, minutes, 0, 0);

					return { ...entry, timestamp: newDate };
				}
				return entry;
			})
		);
	};

	// Filter entries for timeline (exclude backlog items)
	const timelineEntries = entries.filter(
		(entry) => entry.timestamp.getTime() !== 0
	);

	// Filter backlog entries
	const backlogEntries = entries.filter(
		(entry) => entry.timestamp.getTime() === 0
	);

	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
			{/* Header */}
			<header className="bg-white border-b border-gray-200 sticky top-0 z-30 shadow-sm">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex items-center justify-between h-16">
						<div className="flex items-center space-x-3">
							<div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
								<Calendar className="w-5 h-5 text-white" />
							</div>
							<div>
								<h1 className="text-xl font-bold text-gray-900">Timeline</h1>
								<p className="text-xs text-gray-500">Productivity Tracker</p>
							</div>
						</div>

						<div className="flex items-center space-x-2">
							<div className="hidden sm:flex items-center space-x-4 text-sm text-gray-600">
								<span>{timelineEntries.length} entries</span>
								<span>{timeBlocks.length} blocks</span>
								{backlogEntries.length > 0 && (
									<span className="text-purple-600">
										{backlogEntries.length} backlog
									</span>
								)}
							</div>
							<button
								onClick={handleExportPDF}
								className={`p-2 transition-colors duration-200 ${
									timelineEntries.length === 0
										? "text-gray-300 cursor-not-allowed"
										: "text-gray-500 hover:text-blue-600 hover:bg-blue-50"
								} rounded-lg`}
								title={
									timelineEntries.length === 0
										? "Add some entries to export"
										: "Export Timeline as PDF"
								}
								disabled={timelineEntries.length === 0}
							>
								<Download className="w-5 h-5" />
							</button>
							<button
								onClick={() => setIsBacklogModalOpen(true)}
								className="p-2 text-gray-500 hover:text-purple-600 transition-colors duration-200 relative"
								title="Open Backlog"
							>
								<Archive className="w-5 h-5" />
								{backlogEntries.length > 0 && (
									<span className="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
										{backlogEntries.length}
									</span>
								)}
							</button>
							<button className="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200">
								<Settings className="w-5 h-5" />
							</button>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div
					ref={timelineRef}
					className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 sm:p-8"
				>
					<Timeline
						entries={timelineEntries}
						timeBlocks={timeBlocks}
						onToggleComplete={handleToggleComplete}
						onDeleteEntry={handleDeleteEntry}
						onDeleteTimeBlock={handleDeleteTimeBlock}
					/>
				</div>
			</main>

			{/* Floating Action Buttons */}
			<FloatingActionButtons
				onAddEntry={handleFloatingButtonAddEntry}
				onAddTimeBlock={handleFloatingButtonAddTimeBlock}
			/>

			{/* Modals */}
			<AddEntryModal
				isOpen={isAddEntryModalOpen}
				onClose={handleCloseAddEntryModal}
				onAdd={handleAddEntry}
				selectedType={selectedEntryType}
			/>

			<AddTimeBlockModal
				isOpen={isAddTimeBlockModalOpen}
				onClose={() => setIsAddTimeBlockModalOpen(false)}
				onAdd={handleAddTimeBlock}
				existingBlocksCount={timeBlocks.length}
			/>

			<BacklogModal
				isOpen={isBacklogModalOpen}
				onClose={() => setIsBacklogModalOpen(false)}
				backlogEntries={backlogEntries}
				onScheduleEntry={handleScheduleEntry}
				onDeleteEntry={handleDeleteEntry}
				onToggleComplete={handleToggleComplete}
			/>
		</div>
	);
}

export default App;
