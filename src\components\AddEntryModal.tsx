import React, { useState, useEffect } from "react";
import { X, Plus, Calendar, Clock } from "lucide-react";
import { Entry } from "../types";
import { generateId } from "../utils";
import { createPortal } from "react-dom";

interface Props {
	isOpen: boolean;
	onClose: () => void;
	onAdd: (entry: Entry) => void;
	selectedType?: "note" | "task" | "event";
}

export const AddEntryModal: React.FC<Props> = ({
	isOpen,
	onClose,
	onAdd,
	selectedType,
}) => {
	const [type, setType] = useState<"note" | "task" | "event">(
		selectedType || "note"
	);
	const [title, setTitle] = useState("");
	const [content, setContent] = useState("");
	const [selectedDate, setSelectedDate] = useState("today");
	const [time, setTime] = useState("09:00");
	const [showCustomTime, setShowCustomTime] = useState(false);
	const [showTimePickerModal, setShowTimePickerModal] = useState(false);
	const [timePickerMode, setTimePickerMode] = useState<"hour" | "minute">(
		"hour"
	);

	// Smart default time based on current time
	const getSmartDefaultTime = () => {
		const now = new Date();
		const currentHour = now.getHours();

		// Round to next 30-minute interval
		const minutes = now.getMinutes() >= 30 ? 30 : 0;
		const nextHour = minutes === 30 ? currentHour : currentHour + 1;

		return `${nextHour.toString().padStart(2, "0")}:${minutes
			.toString()
			.padStart(2, "0")}`;
	};

	// Initialize with smart default time
	useEffect(() => {
		if (isOpen) {
			setTime(getSmartDefaultTime());
		}
	}, [isOpen]);

	// Quick time adjustment functions
	const adjustTime = (minutes: number) => {
		const [hours, mins] = time.split(":").map(Number);
		const totalMinutes = hours * 60 + mins + minutes;
		const newHours = Math.floor(totalMinutes / 60) % 24;
		const newMins = totalMinutes % 60;
		const newTime = `${newHours.toString().padStart(2, "0")}:${Math.abs(newMins)
			.toString()
			.padStart(2, "0")}`;
		setTime(newTime);
		setShowCustomTime(true); // Show custom time when adjusting
	};

	// Set current time
	const setCurrentTime = () => {
		const now = new Date();
		const currentTime = `${now.getHours().toString().padStart(2, "0")}:${now
			.getMinutes()
			.toString()
			.padStart(2, "0")}`;
		setTime(currentTime);
		setShowCustomTime(false);
	};

	const formatTimeDisplay = (timeValue: string) => {
		const [hours, minutes] = timeValue.split(":").map(Number);
		const period = hours >= 12 ? "PM" : "AM";
		const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
		return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
	};

	const getDateForOption = (option: string): Date => {
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		switch (option) {
			case "tomorrow":
				return new Date(today.getTime() + 24 * 60 * 60 * 1000);
			case "backlog":
				return new Date(0); // Special date for backlog items
			default: // 'today'
				return today;
		}
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (!title.trim()) return;

		let timestamp: Date;

		if (selectedDate === "backlog") {
			timestamp = new Date(0); // Special timestamp for backlog
		} else {
			const baseDate = getDateForOption(selectedDate);
			const [hours, minutes] = time.split(":").map(Number);
			timestamp = new Date(baseDate);
			timestamp.setHours(hours, minutes, 0, 0);
		}

		const entry: Entry = {
			id: generateId(),
			type,
			title: title.trim(),
			content: content.trim() || undefined,
			timestamp,
			completed: false,
		};

		onAdd(entry);
		setTitle("");
		setContent("");
		setSelectedDate("today");
		setTime(getSmartDefaultTime());
		setShowCustomTime(false);
		onClose();
	};

	if (!isOpen) return null;

	return (
		<>
			<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
				<div className="bg-white rounded-xl shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
					<div className="flex items-center justify-between p-6 border-b border-gray-100">
						<h2 className="text-lg font-semibold text-gray-900">
							Add New Entry
						</h2>
						<button
							onClick={onClose}
							className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
						>
							<X className="w-5 h-5" />
						</button>
					</div>

					<form onSubmit={handleSubmit} className="p-6 space-y-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Type
							</label>
							<div className="flex space-x-2">
								{(["note", "task", "event"] as const).map((t) => (
									<button
										key={t}
										type="button"
										onClick={() => setType(t)}
										className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
											type === t
												? "bg-blue-500 text-white shadow-md"
												: "bg-gray-100 text-gray-600 hover:bg-gray-200"
										}`}
									>
										{t.charAt(0).toUpperCase() + t.slice(1)}
									</button>
								))}
							</div>
						</div>

						<div>
							<label
								htmlFor="title"
								className="block text-sm font-medium text-gray-700 mb-1"
							>
								Title
							</label>
							<input
								type="text"
								id="title"
								value={title}
								onChange={(e) => setTitle(e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
								placeholder="Enter title..."
								required
							/>
						</div>

						<div>
							<label
								htmlFor="content"
								className="block text-sm font-medium text-gray-700 mb-1"
							>
								Content
							</label>
							<textarea
								id="content"
								value={content}
								onChange={(e) => setContent(e.target.value)}
								rows={3}
								className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
								placeholder="Enter content..."
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								<Calendar className="w-4 h-4 inline mr-1" />
								Date
							</label>
							<div className="flex space-x-2">
								{[
									{ value: "today", label: "Today" },
									{ value: "tomorrow", label: "Tomorrow" },
									{ value: "backlog", label: "Backlog" },
								].map((option) => (
									<button
										key={option.value}
										type="button"
										onClick={() => setSelectedDate(option.value)}
										className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
											selectedDate === option.value
												? "bg-blue-500 text-white shadow-md"
												: "bg-gray-100 text-gray-600 hover:bg-gray-200"
										}`}
									>
										{option.label}
									</button>
								))}
							</div>
						</div>

						{selectedDate !== "backlog" && (
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									<Clock className="w-4 h-4 inline mr-1" />
									Time
								</label>

								{/* Time Selection */}
								<div className="flex space-x-3 mb-3">
									{/* Now Button */}
									<button
										type="button"
										onClick={setCurrentTime}
										className="px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-green-50 text-green-700 hover:bg-green-100 border border-green-200"
									>
										Now
									</button>

									{/* Clickable Time Display */}
									<button
										type="button"
										onClick={() => {
											setTimePickerMode("hour");
											setShowTimePickerModal(true);
										}}
										className="flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-blue-50 text-blue-700 hover:bg-blue-100 border border-blue-200 flex items-center justify-center"
									>
										<Clock className="w-4 h-4 mr-2" />
										{formatTimeDisplay(time)}
									</button>
								</div>

								{/* Current Time Display with Quick Adjustments */}
								<div className="bg-gray-50 rounded-lg p-3 mb-3">
									<div className="flex items-center justify-between mb-2">
										<div className="flex items-center space-x-2">
											<Clock className="w-4 h-4 text-gray-500" />
											<span className="text-lg font-semibold text-gray-800">
												{formatTimeDisplay(time)}
											</span>
										</div>
									</div>

									{/* Quick Time Adjustment Buttons */}
									<div className="flex items-center justify-center space-x-2">
										<button
											type="button"
											onClick={() => adjustTime(-15)}
											className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100 transition-colors duration-200"
										>
											-15m
										</button>
										<button
											type="button"
											onClick={() => adjustTime(-5)}
											className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100 transition-colors duration-200"
										>
											-5m
										</button>
										<span className="text-xs text-gray-500 px-2">adjust</span>
										<button
											type="button"
											onClick={() => adjustTime(5)}
											className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100 transition-colors duration-200"
										>
											+5m
										</button>
										<button
											type="button"
											onClick={() => adjustTime(15)}
											className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100 transition-colors duration-200"
										>
											+15m
										</button>
									</div>
								</div>
							</div>
						)}

						<div className="flex space-x-3 pt-4">
							<button
								type="button"
								onClick={onClose}
								className="flex-1 px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
							>
								Cancel
							</button>
							<button
								type="submit"
								className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center justify-center space-x-2"
							>
								<Plus className="w-4 h-4" />
								<span>Add Entry</span>
							</button>
						</div>
					</form>
				</div>
			</div>

			{showTimePickerModal &&
				createPortal(
					<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60] p-4">
						<div className="bg-white rounded-xl shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto p-6">
							<div className="flex items-center justify-between mb-4">
								<h3 className="text-lg font-semibold text-gray-900">
									{timePickerMode === "hour" ? "Select Hour" : "Select Minute"}
								</h3>
								<button
									onClick={() => setShowTimePickerModal(false)}
									className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
								>
									<X className="w-5 h-5" />
								</button>
							</div>

							{timePickerMode === "hour" ? (
								<div className="mb-4">
									<div className="relative w-full h-72 mx-auto">
										<div className="absolute inset-0 rounded-full border border-gray-200 flex items-center justify-center">
											<div className="w-3/4 h-3/4 rounded-full border border-gray-200 relative">
												{/* Inner circle (12-23) */}
												{Array.from({ length: 12 }, (_, i) => i + 12).map(
													(hour) => (
														<button
															key={hour}
															type="button"
															onClick={() => {
																const [_, minutes] = time
																	.split(":")
																	.map(Number);
																setTime(
																	`${hour.toString().padStart(2, "0")}:${minutes
																		.toString()
																		.padStart(2, "0")}`
																);
																setTimePickerMode("minute");
															}}
															className={`absolute transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full flex items-center justify-center text-sm
												${
													time.split(":")[0] ===
													hour.toString().padStart(2, "0")
														? "bg-blue-500 text-white"
														: "bg-gray-100 hover:bg-gray-200 text-gray-700"
												}`}
															style={{
																left: `${
																	50 + 35 * Math.sin((hour / 12) * 2 * Math.PI)
																}%`,
																top: `${
																	50 - 35 * Math.cos((hour / 12) * 2 * Math.PI)
																}%`,
															}}
														>
															{hour}
														</button>
													)
												)}
											</div>

											{/* Outer circle (1-11, 0) */}
											{Array.from({ length: 12 }, (_, i) =>
												i === 0 ? 0 : i
											).map((hour) => (
												<button
													key={hour}
													type="button"
													onClick={() => {
														const [_, minutes] = time.split(":").map(Number);
														setTime(
															`${hour.toString().padStart(2, "0")}:${minutes
																.toString()
																.padStart(2, "0")}`
														);
														setTimePickerMode("minute");
													}}
													className={`absolute transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full flex items-center justify-center text-sm
											${
												time.split(":")[0] === hour.toString().padStart(2, "0")
													? "bg-blue-500 text-white"
													: "bg-gray-100 hover:bg-gray-200 text-gray-700"
											}`}
													style={{
														left: `${
															50 + 45 * Math.sin((hour / 12) * 2 * Math.PI)
														}%`,
														top: `${
															50 - 45 * Math.cos((hour / 12) * 2 * Math.PI)
														}%`,
													}}
												>
													{hour === 0 ? "00" : hour}
												</button>
											))}
										</div>
									</div>
								</div>
							) : (
								<div>
									<div className="relative w-full h-72 mx-auto">
										<div className="absolute inset-0 rounded-full border border-gray-200 flex items-center justify-center">
											{Array.from({ length: 60 }, (_, i) => i).map((minute) => {
												// Only show every 5th minute number to avoid crowding
												const showLabel = minute % 5 === 0;
												return (
													<button
														key={minute}
														type="button"
														onClick={() => {
															const [hours, _] = time.split(":").map(Number);
															setTime(
																`${hours.toString().padStart(2, "0")}:${minute
																	.toString()
																	.padStart(2, "0")}`
															);
															setShowTimePickerModal(false);
														}}
														className={`absolute transform -translate-x-1/2 -translate-y-1/2 
												${showLabel ? "w-10 h-10" : "w-2 h-2"} 
												rounded-full flex items-center justify-center 
												${
													time.split(":")[1] ===
													minute.toString().padStart(2, "0")
														? "bg-blue-500 text-white"
														: showLabel
														? "bg-gray-100 hover:bg-gray-200 text-gray-700"
														: "bg-gray-300 hover:bg-gray-400"
												}`}
														style={{
															left: `${
																50 + 45 * Math.sin((minute / 60) * 2 * Math.PI)
															}%`,
															top: `${
																50 - 45 * Math.cos((minute / 60) * 2 * Math.PI)
															}%`,
														}}
													>
														{showLabel ? minute : ""}
													</button>
												);
											})}
										</div>
									</div>
								</div>
							)}

							<div className="flex justify-between mt-6">
								{timePickerMode === "minute" && (
									<button
										type="button"
										onClick={() => setTimePickerMode("hour")}
										className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
									>
										Back to Hours
									</button>
								)}
								<button
									type="button"
									onClick={() => setShowTimePickerModal(false)}
									className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 ml-auto"
								>
									Done
								</button>
							</div>
						</div>
					</div>,
					document.body
				)}
		</>
	);
};
