import React, { useState } from 'react';
import { X, Archive, Calendar, Clock, Circle, CheckSquare, Flag, Edit3, Trash2 } from 'lucide-react';
import { Entry } from '../types';
import { formatTime } from '../utils';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  backlogEntries: Entry[];
  onScheduleEntry: (entryId: string, date: string, time: string) => void;
  onDeleteEntry: (id: string) => void;
  onToggleComplete: (id: string) => void;
}

export const BacklogModal: React.FC<Props> = ({ 
  isOpen, 
  onClose, 
  backlogEntries, 
  onScheduleEntry, 
  onDeleteEntry,
  onToggleComplete 
}) => {
  const [schedulingEntry, setSchedulingEntry] = useState<string | null>(null);
  const [scheduleDate, setScheduleDate] = useState('today');
  const [scheduleTime, setScheduleTime] = useState('09:00');

  const handleSchedule = (entryId: string) => {
    onScheduleEntry(entryId, scheduleDate, scheduleTime);
    setSchedulingEntry(null);
    setScheduleDate('today');
    setScheduleTime('09:00');
  };

  const getIcon = (entry: Entry) => {
    switch (entry.type) {
      case 'note':
        return <Circle className="w-4 h-4 fill-blue-500 text-blue-500" />;
      case 'task':
        return (
          <button
            onClick={() => onToggleComplete(entry.id)}
            className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-all duration-200 hover:scale-110 ${
              entry.completed
                ? 'bg-emerald-500 border-emerald-500 text-white'
                : 'border-gray-300 hover:border-emerald-400'
            }`}
          >
            {entry.completed && <CheckSquare className="w-3 h-3" />}
          </button>
        );
      case 'event':
        return <Flag className="w-4 h-4 text-orange-500 fill-orange-500" />;
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <Archive className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Backlog</h2>
              <p className="text-sm text-gray-500">{backlogEntries.length} unscheduled items</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {backlogEntries.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Archive className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No backlog items</h3>
              <p className="text-sm text-gray-500 text-center max-w-sm">
                Items added to backlog will appear here. You can schedule them for specific dates and times.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {backlogEntries.map((entry) => (
                <div key={entry.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-start space-x-3">
                    <div className="mt-1">
                      {getIcon(entry)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-xs text-gray-500 uppercase tracking-wide font-medium">
                          {entry.type}
                        </span>
                      </div>
                      <h3 className={`font-medium text-gray-900 mb-1 ${entry.completed ? 'line-through opacity-60' : ''}`}>
                        {entry.title}
                      </h3>
                      {entry.content && (
                        <p className={`text-sm text-gray-600 mb-3 ${entry.completed ? 'line-through opacity-60' : ''}`}>
                          {entry.content}
                        </p>
                      )}
                      
                      {schedulingEntry === entry.id ? (
                        <div className="bg-white rounded-lg p-3 border border-gray-200 space-y-3">
                          <div className="flex space-x-2">
                            {[
                              { value: 'today', label: 'Today' },
                              { value: 'tomorrow', label: 'Tomorrow' },
                            ].map((option) => (
                              <button
                                key={option.value}
                                type="button"
                                onClick={() => setScheduleDate(option.value)}
                                className={`flex-1 px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                                  scheduleDate === option.value
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                }`}
                              >
                                {option.label}
                              </button>
                            ))}
                          </div>
                          <input
                            type="time"
                            value={scheduleTime}
                            onChange={(e) => setScheduleTime(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                          />
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleSchedule(entry.id)}
                              className="flex-1 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm flex items-center justify-center space-x-1"
                            >
                              <Calendar className="w-3 h-3" />
                              <span>Schedule</span>
                            </button>
                            <button
                              onClick={() => setSchedulingEntry(null)}
                              className="px-3 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-sm"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setSchedulingEntry(entry.id)}
                            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors duration-200 flex items-center space-x-1"
                          >
                            <Calendar className="w-3 h-3" />
                            <span>Schedule</span>
                          </button>
                          <button
                            onClick={() => onDeleteEntry(entry.id)}
                            className="p-1 text-gray-400 hover:text-red-500 transition-colors duration-200"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};