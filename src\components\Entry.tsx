import React from 'react';
import { Circle, Check, Flag, Edit3, Trash2 } from 'lucide-react';
import { Entry } from '../types';
import { formatTime } from '../utils';

interface Props {
  entry: Entry;
  onToggleComplete: (id: string) => void;
  onDelete: (id: string) => void;
}

export const EntryComponent: React.FC<Props> = ({ entry, onToggleComplete, onDelete }) => {
  const getIcon = () => {
    switch (entry.type) {
      case 'note':
        return <Circle className="w-4 h-4 fill-blue-500 text-blue-500" />;
      case 'task':
        return (
          <button
            onClick={() => onToggleComplete(entry.id)}
            className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-all duration-200 hover:scale-110 ${
              entry.completed
                ? 'bg-emerald-500 border-emerald-500 text-white'
                : 'border-gray-300 hover:border-emerald-400'
            }`}
          >
            {entry.completed && <Check className="w-3 h-3" />}
          </button>
        );
      case 'event':
        return <Flag className="w-4 h-4 text-orange-500 fill-orange-500" />;
      default:
        return null;
    }
  };

  const getTypeColor = () => {
    switch (entry.type) {
      case 'note':
        return 'text-blue-600';
      case 'task':
        return entry.completed ? 'text-emerald-600' : 'text-gray-600';
      case 'event':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="flex items-start space-x-4 group">
      <div className="flex flex-col items-center">
        <div className="relative z-10 bg-white p-2 rounded-full shadow-sm border border-gray-100">
          {getIcon()}
        </div>
      </div>
      
      <div className="flex-1 bg-white rounded-lg border border-gray-100 p-4 shadow-sm hover:shadow-md transition-all duration-200">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-xs text-gray-500 uppercase tracking-wide font-medium">
                {entry.type}
              </span>
              <span className="text-xs text-gray-400">
                {formatTime(entry.timestamp)}
              </span>
            </div>
            <h3 className={`font-medium text-gray-900 mb-1 ${entry.completed ? 'line-through opacity-60' : ''}`}>
              {entry.title}
            </h3>
            {entry.content && (
              <p className={`text-sm text-gray-600 ${entry.completed ? 'line-through opacity-60' : ''}`}>
                {entry.content}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200">
              <Edit3 className="w-3 h-3" />
            </button>
            <button 
              onClick={() => onDelete(entry.id)}
              className="p-1 text-gray-400 hover:text-red-500 transition-colors duration-200"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};