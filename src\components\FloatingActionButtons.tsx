import React, { useState } from 'react';
import { Plus, FileText, CheckSquare, Flag, Clock } from 'lucide-react';

interface Props {
  onAddEntry: (type: 'note' | 'task' | 'event') => void;
  onAddTimeBlock: () => void;
}

export const FloatingActionButtons: React.FC<Props> = ({ onAddEntry, onAddTimeBlock }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const buttons = [
    { type: 'note' as const, icon: FileText, label: 'Note', color: 'bg-blue-500 hover:bg-blue-600' },
    { type: 'task' as const, icon: CheckSquare, label: 'Task', color: 'bg-emerald-500 hover:bg-emerald-600' },
    { type: 'event' as const, icon: Flag, label: 'Event', color: 'bg-orange-500 hover:bg-orange-600' },
  ];

  return (
    <div className="fixed bottom-6 right-6 flex flex-col items-end space-y-3 z-40">
      {/* Time Block Button */}
      <button
        onClick={onAddTimeBlock}
        className="bg-indigo-500 hover:bg-indigo-600 text-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
        title="Add Time Block"
      >
        <Clock className="w-5 h-5" />
      </button>

      {/* Entry Type Buttons */}
      {isExpanded && (
        <div className="flex flex-col items-end space-y-2 animate-in slide-in-from-bottom-2 duration-200">
          {buttons.map(({ type, icon: Icon, label, color }) => (
            <button
              key={type}
              onClick={() => {
                onAddEntry(type);
                setIsExpanded(false);
              }}
              className={`${color} text-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2 pr-4`}
              title={`Add ${label}`}
            >
              <Icon className="w-4 h-4" />
              <span className="text-sm font-medium">{label}</span>
            </button>
          ))}
        </div>
      )}

      {/* Main Add Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`bg-blue-500 hover:bg-blue-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 ${
          isExpanded ? 'rotate-45' : ''
        }`}
        title="Add Entry"
      >
        <Plus className="w-6 h-6" />
      </button>
    </div>
  );
};