import React from 'react';
import { Clock, X } from 'lucide-react';
import { TimeBlock, Entry } from '../types';
import { formatTime } from '../utils';

interface Props {
  timeBlock: TimeBlock;
  entries: Entry[];
  onDelete: (id: string) => void;
  children: React.ReactNode;
}

export const TimeBlockComponent: React.FC<Props> = ({ timeBlock, entries, onDelete, children }) => {
  return (
    <div className={`relative border-2 border-dashed rounded-xl p-6 mb-6 ${timeBlock.color}`}>
      <div className="absolute -top-3 left-4 bg-white px-3 py-1 rounded-full border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-2">
          <Clock className="w-3 h-3 text-gray-500" />
          <span className="text-xs font-medium text-gray-700">{timeBlock.name}</span>
          <span className="text-xs text-gray-500">
            {formatTime(timeBlock.startTime)} - {formatTime(timeBlock.endTime)}
          </span>
          <button
            onClick={() => onDelete(timeBlock.id)}
            className="text-gray-400 hover:text-red-500 transition-colors duration-200"
          >
            <X className="w-3 h-3" />
          </button>
        </div>
      </div>
      
      <div className="space-y-4 mt-2">
        {children}
      </div>
    </div>
  );
};