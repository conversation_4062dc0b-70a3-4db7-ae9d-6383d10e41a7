import React from 'react';
import { Plus } from 'lucide-react';
import { Entry, TimeBlock } from '../types';
import { EntryComponent } from './Entry';
import { TimeBlockComponent } from './TimeBlock';
import { isWithinTimeBlock, formatDate } from '../utils';

interface Props {
  entries: Entry[];
  timeBlocks: TimeBlock[];
  onToggleComplete: (id: string) => void;
  onDeleteEntry: (id: string) => void;
  onDeleteTimeBlock: (id: string) => void;
}

export const Timeline: React.FC<Props> = ({
  entries,
  timeBlocks,
  onToggleComplete,
  onDeleteEntry,
  onDeleteTimeBlock,
}) => {
  // Sort entries by timestamp
  const sortedEntries = [...entries].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  
  // Sort time blocks by start time
  const sortedTimeBlocks = [...timeBlocks].sort((a, b) => a.startTime.getTime() - b.startTime.getTime());

  // Group entries by date
  const entriesByDate = sortedEntries.reduce((acc, entry) => {
    const dateKey = entry.timestamp.toDateString();
    if (!acc[dateKey]) acc[dateKey] = [];
    acc[dateKey].push(entry);
    return acc;
  }, {} as Record<string, Entry[]>);

  const renderEntriesForDate = (dateEntries: Entry[]) => {
    // Find entries that belong to time blocks
    const entriesInBlocks = new Set<string>();
    const blockEntries = sortedTimeBlocks.map(block => ({
      block,
      entries: dateEntries.filter(entry => {
        const inBlock = isWithinTimeBlock(entry.timestamp, block);
        if (inBlock) entriesInBlocks.add(entry.id);
        return inBlock;
      }),
    })).filter(({ entries }) => entries.length > 0);

    // Entries not in any time block
    const freeEntries = dateEntries.filter(entry => !entriesInBlocks.has(entry.id));

    return (
      <div className="space-y-6">
        {/* Render time blocks with their entries */}
        {blockEntries.map(({ block, entries }) => (
          <TimeBlockComponent
            key={block.id}
            timeBlock={block}
            entries={entries}
            onDelete={onDeleteTimeBlock}
          >
            {entries.map((entry, index) => (
              <div key={entry.id} className="relative">
                {index < entries.length - 1 && (
                  <div className="absolute left-4 top-full w-0.5 h-6 bg-gray-200" />
                )}
                <EntryComponent
                  entry={entry}
                  onToggleComplete={onToggleComplete}
                  onDelete={onDeleteEntry}
                />
              </div>
            ))}
          </TimeBlockComponent>
        ))}

        {/* Render free entries */}
        {freeEntries.map((entry, index) => (
          <div key={entry.id} className="relative">
            {(index < freeEntries.length - 1 || blockEntries.length > 0) && (
              <div className="absolute left-4 top-full w-0.5 h-6 bg-gray-200" />
            )}
            <EntryComponent
              entry={entry}
              onToggleComplete={onToggleComplete}
              onDelete={onDeleteEntry}
            />
          </div>
        ))}
      </div>
    );
  };

  if (sortedEntries.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4 mx-auto">
            <Plus className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No entries yet</h3>
          <p className="text-sm text-gray-500 max-w-sm">
            Start by adding your first note, task, or event using the buttons below.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {Object.entries(entriesByDate).map(([date, dateEntries]) => (
        <div key={date} className="relative">
          <div className="sticky top-0 bg-white bg-opacity-90 backdrop-blur-sm z-10 py-2 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              {formatDate(new Date(date))}
            </h2>
          </div>
          {renderEntriesForDate(dateEntries)}
        </div>
      ))}
    </div>
  );
};