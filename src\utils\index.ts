export const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  });
};

export const isWithinTimeBlock = (entryTime: Date, timeBlock: TimeBlock): boolean => {
  return entryTime >= timeBlock.startTime && entryTime <= timeBlock.endTime;
};

export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

export const getTimeBlockColor = (index: number): string => {
  const colors = [
    'bg-blue-50 border-blue-200',
    'bg-emerald-50 border-emerald-200', 
    'bg-purple-50 border-purple-200',
    'bg-orange-50 border-orange-200',
    'bg-pink-50 border-pink-200',
    'bg-indigo-50 border-indigo-200',
  ];
  return colors[index % colors.length];
};