import html2canvas from "html2canvas";
import jsPDF from "jspdf";

export interface ExportOptions {
	filename?: string;
	quality?: number;
	scale?: number;
}

export const exportTimelineToPDF = async (
	timelineElement: HTMLElement,
	options: ExportOptions = {}
): Promise<void> => {
	const {
		filename = `timeline-${new Date().toISOString().split("T")[0]}.pdf`,
		quality = 0.95,
		scale = 2,
	} = options;

	try {
		// Show loading state (you can customize this)
		const loadingToast = document.createElement("div");
		loadingToast.className =
			"fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50";
		loadingToast.textContent = "Generating PDF...";
		document.body.appendChild(loadingToast);

		// Capture the timeline element as canvas
		const canvas = await html2canvas(timelineElement, {
			scale: scale,
			useCORS: true,
			allowTaint: true,
			backgroundColor: "#ffffff",
			logging: false,
			width: timelineElement.scrollWidth,
			height: timelineElement.scrollHeight,
			scrollX: 0,
			scrollY: 0,
		});

		// Calculate PDF dimensions
		const imgWidth = canvas.width;
		const imgHeight = canvas.height;

		// A4 size in mm
		const pdfWidth = 210;
		const pdfHeight = 297;

		// Calculate scaling to fit content properly
		const ratio = Math.min(
			pdfWidth / (imgWidth * 0.264583),
			pdfHeight / (imgHeight * 0.264583)
		);
		const scaledWidth = imgWidth * 0.264583 * ratio;
		const scaledHeight = imgHeight * 0.264583 * ratio;

		// Create PDF
		const pdf = new jsPDF({
			orientation: scaledHeight > scaledWidth ? "portrait" : "landscape",
			unit: "mm",
			format: "a4",
		});

		// Add title and metadata
		const currentDate = new Date().toLocaleDateString("en-US", {
			weekday: "long",
			year: "numeric",
			month: "long",
			day: "numeric",
		});
		const currentTime = new Date().toLocaleTimeString("en-US", {
			hour: "2-digit",
			minute: "2-digit",
		});

		pdf.setFontSize(18);
		pdf.setFont("helvetica", "bold");
		pdf.text("Timeline - Productivity Tracker", 20, 20);

		pdf.setFontSize(10);
		pdf.setFont("helvetica", "normal");
		pdf.text(`Exported on ${currentDate} at ${currentTime}`, 20, 30);

		// Add a subtle line separator
		pdf.setDrawColor(200, 200, 200);
		pdf.line(20, 35, actualPdfWidth - 20, 35);

		// Add the timeline image
		const imgData = canvas.toDataURL("image/jpeg", quality);

		// Calculate position to center the image
		const actualPdfWidth = pdf.internal.pageSize.getWidth();
		const actualPdfHeight = pdf.internal.pageSize.getHeight();
		const xPos = (actualPdfWidth - scaledWidth) / 2;
		const yPos = 45; // Start below the title and separator line

		// Check if image fits on one page
		if (scaledHeight + yPos <= actualPdfHeight - 20) {
			// Single page
			pdf.addImage(imgData, "JPEG", xPos, yPos, scaledWidth, scaledHeight);
		} else {
			// Multiple pages needed
			const pageHeight = actualPdfHeight - yPos - 20;
			const totalPages = Math.ceil(scaledHeight / pageHeight);

			for (let i = 0; i < totalPages; i++) {
				if (i > 0) {
					pdf.addPage();
				}

				const sourceY = (i * pageHeight * imgHeight) / scaledHeight;
				const sourceHeight = Math.min(
					(pageHeight * imgHeight) / scaledHeight,
					imgHeight - sourceY
				);
				const destHeight = (sourceHeight * scaledHeight) / imgHeight;

				// Create a temporary canvas for this page section
				const pageCanvas = document.createElement("canvas");
				pageCanvas.width = imgWidth;
				pageCanvas.height = sourceHeight;
				const pageCtx = pageCanvas.getContext("2d");

				if (pageCtx) {
					pageCtx.drawImage(
						canvas,
						0,
						sourceY,
						imgWidth,
						sourceHeight,
						0,
						0,
						imgWidth,
						sourceHeight
					);
					const pageImgData = pageCanvas.toDataURL("image/jpeg", quality);
					pdf.addImage(
						pageImgData,
						"JPEG",
						xPos,
						yPos,
						scaledWidth,
						destHeight
					);
				}
			}
		}

		// Save the PDF
		pdf.save(filename);

		// Remove loading toast
		document.body.removeChild(loadingToast);

		// Show success toast
		const successToast = document.createElement("div");
		successToast.className =
			"fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50";
		successToast.textContent = "PDF exported successfully!";
		document.body.appendChild(successToast);

		setTimeout(() => {
			if (document.body.contains(successToast)) {
				document.body.removeChild(successToast);
			}
		}, 3000);
	} catch (error) {
		console.error("Error exporting PDF:", error);

		// Show error toast
		const errorToast = document.createElement("div");
		errorToast.className =
			"fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50";
		errorToast.textContent = "Failed to export PDF. Please try again.";
		document.body.appendChild(errorToast);

		setTimeout(() => {
			if (document.body.contains(errorToast)) {
				document.body.removeChild(errorToast);
			}
		}, 3000);
	}
};
